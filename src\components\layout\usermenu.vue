<script setup>
import { ref, computed } from "vue"
import { useAuthStore } from "@/stores/authstore"
import { useRouter } from "vue-router"

// 状态管理
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const userMenuOpen = ref(false)

// 计算属性
const userEmail = computed(() => {
  // 从用户信息中获取邮箱，如果没有则显示默认邮箱
  return authStore.user?.email || "xia*******<EMAIL>"
})

const userName = computed(() => {
  return authStore.user?.name || "白熊长衫"
})

// 菜单项数据
const menuItems = ref([
  {
    icon: "mdi-cog",
    title: "系统设置",
    action: "settings",
  },
  {
    icon: "mdi-send",
    title: "联系我们",
    action: "contact",
  },
  {
    icon: "mdi-logout",
    title: "退出登录",
    action: "logout",
    color: "error",
  },
  {
    icon: "mdi-account",
    title: "个人信息",
    action: "profile",
  },
])

// 方法
const handleMenuClick = (action) => {
  userMenuOpen.value = false

  switch (action) {
    case "logout":
      authStore.logout()
      router.push({ name: "login" })
      break
    case "profile":
      router.push({ name: "userprofile" })
      break
    case "settings":
      router.push({ name: "usersettings" })
      break
    case "contact":
      router.push({ name: "contactus" })
      break
    default:
      console.log("未知操作:", action)
  }
}

// 兼容旧方法
const handleLogout = () => handleMenuClick("logout")
const handleProfile = () => handleMenuClick("profile")
const handleSettings = () => handleMenuClick("settings")
const handleContact = () => handleMenuClick("contact")
</script>

<template>
  <VMenu v-model="userMenuOpen" :close-on-content-click="false" location="bottom end" offset="8">
    <template #activator="{ props }">
      <VBtn v-bind="props" variant="text" color="white" class="text-none user-menu-trigger">
        <span class="mr-1">{{ userName }}</span>
        <VIcon :icon="userMenuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down'" size="16" />
      </VBtn>
    </template>

    <VCard min-width="260" class="user-menu-card">
      <!-- 用户信息头部 -->
      <div class="user-info-header">
        <div class="user-avatar">
          <VAvatar size="48" color="primary" class="user-avatar-circle">
            <VIcon icon="mdi-account" size="24" color="white" />
          </VAvatar>
        </div>
        <div class="user-details">
          <div class="user-name">{{ userName }}</div>
          <div class="user-email">{{ userEmail }}</div>
        </div>
      </div>

      <VDivider class="menu-divider" />

      <!-- 菜单项 -->
      <VList density="compact" class="user-menu-list">
        <VListItem
          v-for="item in menuItems"
          :key="item.action"
          :prepend-icon="item.icon"
          :title="item.title"
          :class="['menu-item', { 'menu-item--danger': item.color === 'error' }]"
          @click="handleMenuClick(item.action)">
          <template #prepend>
            <VIcon :icon="item.icon" :color="item.color || 'default'" size="20" />
          </template>
        </VListItem>
      </VList>
    </VCard>
  </VMenu>
</template>

<style scoped>
.user-menu-trigger {
  text-transform: none !important;
}

.user-menu-card {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.user-info-header {
  display: flex;
  align-items: center;
  padding: 20px 16px;
  gap: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.user-avatar {
  flex-shrink: 0;
}

.user-avatar-circle {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.2;
}

.user-email {
  font-size: 13px;
  color: #6c757d;
  word-break: break-all;
  line-height: 1.3;
}

.menu-divider {
  margin: 0;
  border-color: rgba(0, 0, 0, 0.08);
}

.user-menu-list {
  padding: 8px 0;
  background: white;
}

.menu-item {
  padding: 12px 16px;
  min-height: 48px;
  transition: all 0.2s ease;
  border-radius: 0;
}

.menu-item:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

.menu-item--danger:hover {
  background-color: rgba(244, 67, 54, 0.04);
}

.menu-item .v-list-item__prepend {
  margin-right: 12px;
}

.menu-item .v-icon {
  color: #6c757d;
  transition: color 0.2s ease;
}

.menu-item--danger .v-icon {
  color: #f44336;
}

.menu-item:hover .v-icon {
  color: #1976d2;
}

.menu-item--danger:hover .v-icon {
  color: #f44336;
}
</style>
