<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authstore'

// 状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const editMode = ref(false)

// 用户信息表单
const userForm = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  bio: '',
  avatar: null
})

// 计算属性
const userInfo = computed(() => authStore.user || {})

// 方法
const handleBack = () => {
  router.go(-1)
}

const toggleEditMode = () => {
  if (editMode.value) {
    // 取消编辑，恢复原始数据
    loadUserData()
  }
  editMode.value = !editMode.value
}

const loadUserData = () => {
  // 从store或API加载用户数据
  userForm.value = {
    name: userInfo.value.name || '白熊长衫',
    email: userInfo.value.email || 'xia*******<EMAIL>',
    phone: userInfo.value.phone || '138****8888',
    department: userInfo.value.department || '医疗AI部门',
    position: userInfo.value.position || '高级医疗顾问',
    bio: userInfo.value.bio || '专注于超声医疗AI技术研发，致力于为医疗行业提供智能化解决方案。',
    avatar: userInfo.value.avatar || null
  }
}

const handleSave = async () => {
  loading.value = true
  try {
    // 这里调用API保存用户信息
    console.log('保存用户信息:', userForm.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新store中的用户信息
    authStore.updateUser(userForm.value)
    
    editMode.value = false
    console.log('用户信息保存成功')
  } catch (error) {
    console.error('保存用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

const handleAvatarChange = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 这里可以添加头像上传逻辑
    console.log('选择头像文件:', file.name)
  }
}

// 生命周期
onMounted(() => {
  loadUserData()
})
</script>

<template>
  <div class="user-profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <VBtn
        variant="text"
        prepend-icon="mdi-arrow-left"
        class="back-btn"
        @click="handleBack"
      >
        返回
      </VBtn>
      
      <h1 class="page-title">个人信息</h1>
      
      <div class="header-actions">
        <VBtn
          v-if="!editMode"
          color="primary"
          prepend-icon="mdi-pencil"
          @click="toggleEditMode"
        >
          编辑
        </VBtn>
        
        <div v-else class="edit-actions">
          <VBtn
            variant="outlined"
            @click="toggleEditMode"
          >
            取消
          </VBtn>
          <VBtn
            color="primary"
            :loading="loading"
            @click="handleSave"
          >
            保存
          </VBtn>
        </div>
      </div>
    </div>

    <VContainer class="profile-container">
      <VRow>
        <VCol cols="12" md="8" lg="6" class="mx-auto">
          <VCard class="profile-card">
            <!-- 头像部分 -->
            <div class="avatar-section">
              <VAvatar
                size="120"
                color="primary"
                class="profile-avatar"
              >
                <VIcon
                  v-if="!userForm.avatar"
                  icon="mdi-account"
                  size="60"
                  color="white"
                />
                <img
                  v-else
                  :src="userForm.avatar"
                  alt="用户头像"
                />
              </VAvatar>
              
              <div v-if="editMode" class="avatar-upload">
                <VBtn
                  size="small"
                  color="primary"
                  variant="outlined"
                  prepend-icon="mdi-camera"
                  @click="$refs.avatarInput.click()"
                >
                  更换头像
                </VBtn>
                <input
                  ref="avatarInput"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  @change="handleAvatarChange"
                />
              </div>
            </div>

            <VDivider class="my-6" />

            <!-- 用户信息表单 -->
            <VCardText>
              <VForm>
                <VRow>
                  <!-- 姓名 -->
                  <VCol cols="12" sm="6">
                    <VTextField
                      v-model="userForm.name"
                      label="姓名"
                      :readonly="!editMode"
                      :variant="editMode ? 'outlined' : 'plain'"
                      prepend-inner-icon="mdi-account"
                    />
                  </VCol>

                  <!-- 邮箱 -->
                  <VCol cols="12" sm="6">
                    <VTextField
                      v-model="userForm.email"
                      label="邮箱"
                      type="email"
                      :readonly="!editMode"
                      :variant="editMode ? 'outlined' : 'plain'"
                      prepend-inner-icon="mdi-email"
                    />
                  </VCol>

                  <!-- 手机号 -->
                  <VCol cols="12" sm="6">
                    <VTextField
                      v-model="userForm.phone"
                      label="手机号"
                      :readonly="!editMode"
                      :variant="editMode ? 'outlined' : 'plain'"
                      prepend-inner-icon="mdi-phone"
                    />
                  </VCol>

                  <!-- 部门 -->
                  <VCol cols="12" sm="6">
                    <VTextField
                      v-model="userForm.department"
                      label="部门"
                      :readonly="!editMode"
                      :variant="editMode ? 'outlined' : 'plain'"
                      prepend-inner-icon="mdi-office-building"
                    />
                  </VCol>

                  <!-- 职位 -->
                  <VCol cols="12">
                    <VTextField
                      v-model="userForm.position"
                      label="职位"
                      :readonly="!editMode"
                      :variant="editMode ? 'outlined' : 'plain'"
                      prepend-inner-icon="mdi-briefcase"
                    />
                  </VCol>

                  <!-- 个人简介 -->
                  <VCol cols="12">
                    <VTextarea
                      v-model="userForm.bio"
                      label="个人简介"
                      :readonly="!editMode"
                      :variant="editMode ? 'outlined' : 'plain'"
                      prepend-inner-icon="mdi-text"
                      rows="3"
                      auto-grow
                    />
                  </VCol>
                </VRow>
              </VForm>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>

<style scoped>
.user-profile-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.back-btn {
  margin-right: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.profile-container {
  padding-top: 32px;
  padding-bottom: 32px;
}

.profile-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  overflow: hidden;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 24px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.profile-avatar {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  margin-bottom: 16px;
}

.avatar-upload {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .profile-container {
    padding-top: 16px;
    padding-bottom: 16px;
  }
  
  .avatar-section {
    padding: 24px 16px 12px;
  }
  
  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
  }
  
  .profile-avatar .v-icon {
    font-size: 50px !important;
  }
}
</style>
