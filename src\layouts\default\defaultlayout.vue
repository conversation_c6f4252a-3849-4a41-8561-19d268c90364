<script setup>
import { ref } from 'vue'
import Sidebar from '@/components/layout/sidebar.vue'
import MainContent from '@/components/layout/maincontent.vue'
import ChatInput from '@/components/layout/chatinput.vue'

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 处理侧边栏切换
const handleSidebarToggle = (isCollapsed) => {
  sidebarCollapsed.value = isCollapsed
  console.log('侧边栏状态变化:', isCollapsed ? '收缩' : '展开')
}
</script>

<template>
  <VApp>
    <!-- 主体布局 -->
    <VMain>
      <div class="app-layout">
        <!-- 左侧边栏 -->
        <Sidebar @sidebar-toggle="handleSidebarToggle" />

        <!-- 主内容区域 -->
        <div class="content-area">
          <MainContent />
          <ChatInput :sidebar-collapsed="sidebarCollapsed" />
        </div>
      </div>
    </VMain>
  </VApp>
</template>

<style scoped>
.content-area {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-area {
    padding-bottom: 80px;
  }
}
</style>
